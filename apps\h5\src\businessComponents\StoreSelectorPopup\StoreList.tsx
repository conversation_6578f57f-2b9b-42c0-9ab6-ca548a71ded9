'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import type { StoreList, StoreListInput, StoreListItem } from '@ninebot/core'
import {
  appLocalStorage,
  TCatchMessage,
  useDebounceFn,
  useLazyGetStoreV2Query,
  useToastContext,
} from '@ninebot/core'
import { useLocation } from '@ninebot/core/src/businessHooks'
import { resolveCatchMessage } from '@ninebot/core/src/utils/util'

import { Skeleton } from '@/components'

import StoreEmpty from './StoreEmpty'
import StoreItem from './StoreItem'

// ==================== 类型定义 ====================

/**
 * 位置信息接口
 */
interface LocationResult {
  longitude?: number | null
  latitude?: number | null
}

/**
 * 地址信息接口
 */
interface PdpAddress {
  region?: string
  city?: string
  district?: string
}

/**
 * 组件属性接口
 */
interface StoreListProps {
  store: StoreList
  setStore: (store: StoreList) => void
  showAddressPop: () => void
  setBtnLoading: (loading: boolean) => void
  curStore: StoreListItem | null
  setCurStore: (store: StoreListItem | null) => void
  productId: number | string
  address?: PdpAddress
}

/**
 * 分页配置
 */
interface PaginationConfig {
  pageSize: number
  currentPage: number
  totalPages: number
}

// ==================== 常量定义 ====================

/**
 * 本地存储键名
 */
const STORAGE_KEYS = {
  USER_LOCATION: 'pdp_user_location',
  ADDRESS: 'pdp_address',
} as const

/**
 * 骨架屏配置
 */
const SKELETON_CONFIG = {
  count: 3,
  style: {
    borderRadius: 12,
    height: 140,
    width: '100%',
    marginTop: 12,
  },
} as const

// ==================== 主组件 ====================

/**
 * 门店列表组件
 *
 * 功能：
 * 1. 根据地址和位置信息获取门店列表
 * 2. 支持分页加载更多
 * 3. 支持门店选择
 * 4. 提供加载和空状态
 */
const StoreList: React.FC<StoreListProps> = ({
  store,
  setStore,
  showAddressPop,
  setBtnLoading,
  curStore,
  setCurStore,
  productId,
  address,
}) => {
  const getI18nString = useTranslations('Common')
  const toast = useToastContext()
  const { getLocation } = useLocation()
  const [getStore] = useLazyGetStoreV2Query()

  // ==================== 状态管理 ====================

  // 数据状态
  const [allStores, setAllStores] = useState<StoreList>([])
  const [pagination, setPagination] = useState<PaginationConfig>({
    pageSize: 10,
    currentPage: 0,
    totalPages: 0,
  })

  // UI状态
  const [loading, setLoading] = useState<boolean>(true)
  const [noData, setNoData] = useState<boolean>(false)

  // 门店消息状态
  const [storeMsg, setStoreMsg] = useState<string>('')

  // ==================== 计算属性 ====================

  /**
   * 判断门店是否被选中
   */
  const isStoreSelected = useCallback(
    (item: StoreListItem | null): boolean => {
      return !!(curStore && curStore?.store_code === item?.store_code)
    },
    [curStore],
  )

  /**
   * 是否显示加载更多按钮
   */
  const showLoadMore = useMemo(() => {
    return pagination.currentPage < pagination.totalPages && store?.length > 0
  }, [pagination.currentPage, pagination.totalPages, store?.length])

  // ==================== 工具函数 ====================

  /**
   * 从本地存储获取位置信息
   */
  const getLocationFromStorage = useCallback(async (): Promise<LocationResult | null> => {
    try {
      const storedLocation = await appLocalStorage.getItem(STORAGE_KEYS.USER_LOCATION)
      return storedLocation as LocationResult | null
    } catch (error) {
      console.warn('从本地存储获取位置信息失败:', error)
      return null
    }
  }, [])

  /**
   * 从本地存储获取地址信息
   */
  const getAddressFromStorage = useCallback(async (): Promise<PdpAddress | null> => {
    try {
      const addressData = await appLocalStorage.getItem<PdpAddress | null>(STORAGE_KEYS.ADDRESS)
      return addressData || null
    } catch (error) {
      console.warn('从本地存储获取地址信息失败:', error)
      return null
    }
  }, [])

  /**
   * 构建门店列表请求参数
   */
  const buildStoreListParams = useCallback(
    (pdpLocation: LocationResult | null, pdpAddress?: PdpAddress): StoreListInput => {
      const params: StoreListInput = {
        product_id: Number(productId),
      }

      // 添加地址信息
      if (pdpAddress) {
        if (pdpAddress.region) params.region = pdpAddress.region
        if (pdpAddress.city) params.city = pdpAddress.city
        if (pdpAddress.district) params.district = pdpAddress.district
      }

      // 添加位置信息
      if (pdpLocation?.longitude && pdpLocation?.latitude) {
        params.longitude = pdpLocation.longitude.toString()
        params.latitude = pdpLocation.latitude.toString()
      }

      return params
    },
    [productId],
  )

  /**
   * 处理门店列表数据
   */
  const handleStoreListData = useCallback(
    (productStores: (StoreListItem | null)[]) => {
      const filteredStores = productStores.filter(Boolean) as StoreListItem[]
      const storeCount = filteredStores.length

      if (storeCount > 0) {
        setAllStores(filteredStores)
        setPagination((prev) => ({
          ...prev,
          currentPage: 1,
          totalPages: Math.ceil(storeCount / prev.pageSize),
        }))
        setStore(filteredStores.slice(0, pagination.pageSize))
        setNoData(false)
      } else {
        setAllStores([])
        setStore([])
        setNoData(true)
      }
    },
    [setStore, pagination.pageSize],
  )

  // ==================== 事件处理函数 ====================

  /**
   * 加载更多门店
   */
  const handleLoadMore = useCallback(() => {
    const nextPage = pagination.currentPage + 1
    setPagination((prev) => ({ ...prev, currentPage: nextPage }))
    setStore(allStores.slice(0, nextPage * pagination.pageSize))
  }, [pagination.currentPage, pagination.pageSize, allStores, setStore])

  // ==================== 数据获取逻辑 ====================

  /**
   * 获取门店列表
   */
  const fetchStoreList = useCallback(async () => {
    setLoading(true)
    setBtnLoading(true)

    try {
      // 并行获取地址和位置信息
      const [pdpAddressData, pdpLocationData] = await Promise.all([
        getAddressFromStorage(),
        getLocationFromStorage(),
      ])

      // 获取当前位置信息
      let currentLocation: LocationResult | null = pdpLocationData
      if (!currentLocation) {
        try {
          currentLocation = await getLocation()
        } catch (error) {
          console.warn('获取位置信息失败，将使用地址信息获取门店:', error)
          currentLocation = null
        }
      }

      // 构建请求参数
      const params = buildStoreListParams(currentLocation, pdpAddressData || address)

      // 请求门店数据
      const response = await getStore({ input: params }).unwrap()

      if (response?.product_storesV2) {
        const product_stores = response.product_storesV2.items || []
        setStoreMsg(response.product_storesV2.message || '') // 设置消息
        handleStoreListData(product_stores)
      } else {
        setStoreMsg('') // 清空消息
        setNoData(true)
      }
    } catch (error) {
      console.error('获取门店列表失败:', error)
      toast.show({
        icon: 'fail',
        content: resolveCatchMessage(error as TCatchMessage) as string,
      })
      setNoData(true)
    } finally {
      setLoading(false)
      setBtnLoading(false)
    }
  }, [
    getAddressFromStorage,
    getLocationFromStorage,
    getLocation,
    buildStoreListParams,
    address,
    getStore,
    handleStoreListData,
    toast,
    setBtnLoading,
  ])

  // ==================== 副作用 ====================

  // 防抖的门店列表获取
  const { run: debouncedFetchStoreList } = useDebounceFn(fetchStoreList, { wait: 500 })

  /**
   * 监听产品ID和地址变化，重新获取门店列表（使用防抖）
   */
  useEffect(() => {
    debouncedFetchStoreList()

    return () => {
      setLoading(false)
      setBtnLoading(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productId, address?.region, address?.city, address?.district]) // 监听地址的具体字段变化

  // ==================== 渲染函数 ====================

  /**
   * 渲染骨架屏
   */
  const renderSkeleton = useCallback(
    () => (
      <div className="px-base-16 pb-36">
        {Array.from({ length: SKELETON_CONFIG.count }, (_, index) => (
          <Skeleton key={index} style={SKELETON_CONFIG.style} />
        ))}
      </div>
    ),
    [],
  )

  /**
   * 渲染门店列表项
   */
  const renderStoreItem = useCallback(
    (item: StoreListItem) => (
      <StoreItem
        key={item?.store_id}
        store={item}
        isSelected={isStoreSelected(item)}
        onClick={() => setCurStore(item)}
      />
    ),
    [isStoreSelected, setCurStore],
  )

  /**
   * 渲染加载更多按钮
   */
  const renderLoadMoreButton = useCallback(() => {
    if (!showLoadMore) return null

    return (
      <div className="flex cursor-pointer items-center justify-center p-4" onClick={handleLoadMore}>
        <span className="text-base text-primary">{getI18nString('more')}</span>
      </div>
    )
  }, [showLoadMore, handleLoadMore, getI18nString])

  // ==================== 条件渲染 ====================

  // 渲染加载状态
  if (loading) {
    return renderSkeleton()
  }

  // 渲染空状态
  if (noData || allStores.length === 0) {
    return <StoreEmpty isStore onOpenAddressPop={showAddressPop} storeMsg={storeMsg} />
  }

  // ==================== 组件返回 ====================

  return (
    <div className="flex flex-1 flex-col gap-base-12 overflow-y-auto px-base-16 pb-36">
      {/* 门店列表 */}
      {(store.filter(Boolean) as StoreListItem[]).map(renderStoreItem)}

      {/* 加载更多按钮 */}
      {renderLoadMoreButton()}
    </div>
  )
}

export default StoreList
