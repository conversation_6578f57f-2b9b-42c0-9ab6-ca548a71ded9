'use client'

import { ReactNode, useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { NavBar } from 'antd-mobile'
import clsx from 'clsx'

import { Arrow } from '@/components/icons'

type ProductNavBarProps = {
  title?: string
  left?: ReactNode
  right?: ReactNode
  onBack?: () => void
  customStyle?: React.CSSProperties
  productRef?: React.RefObject<HTMLDivElement>
  reviewRef?: React.RefObject<HTMLDivElement>
  detailRef?: React.RefObject<HTMLDivElement>
  recommendRef?: React.RefObject<HTMLDivElement>
  isProduct?: boolean
  hasReview?: boolean
  hasDetail?: boolean
  hasRecommend?: boolean
}

const ProductNavBar = ({
  title,
  left,
  right,
  onBack,
  customStyle = {},
  productRef,
  reviewRef,
  detailRef,
  recommendRef,
  isProduct,
  hasReview = true,
  hasDetail = true,
  hasRecommend = false,
}: ProductNavBarProps) => {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('product')
  const [showTabs, setShowTabs] = useState(false)

  const tickingRef = useRef(false)

  const handleBack = () => {
    if (onBack) {
      onBack()
    } else {
      router.back()
    }
  }

  // 处理滚动事件（rAF 节流 + 被动监听）
  useEffect(() => {
    if (!isProduct) return

    const onScroll = () => {
      if (tickingRef.current) return
      tickingRef.current = true
      requestAnimationFrame(() => {
        if (!productRef?.current) {
          tickingRef.current = false
          return
        }

        const { top } = productRef.current.getBoundingClientRect()
        // 当商品区域完全滚出视图时显示吸顶导航
        setShowTabs(top < -300)

        // 根据滚动位置更新当前激活的标签
        const scrollPosition = window.scrollY + 100 // 提前触发
        const reviewPosition = reviewRef?.current?.offsetTop || 0
        const detailPosition = detailRef?.current?.offsetTop || 0
        const recommendPosition = recommendRef?.current?.offsetTop || 0

        if (
          detailPosition < scrollPosition &&
          scrollPosition >= recommendPosition &&
          hasRecommend
        ) {
          setActiveTab('recommend')
        } else if (
          detailPosition < scrollPosition &&
          scrollPosition >= reviewPosition &&
          hasReview
        ) {
          setActiveTab('review')
        } else if (scrollPosition >= detailPosition && hasDetail) {
          setActiveTab('detail')
        } else {
          setActiveTab('product')
        }

        tickingRef.current = false
      })
    }

    window.addEventListener('scroll', onScroll, { passive: true })
    return () => window.removeEventListener('scroll', onScroll)
  }, [
    isProduct,
    productRef,
    detailRef,
    reviewRef,
    hasReview,
    hasDetail,
    recommendRef,
    hasRecommend,
  ])

  // 处理标签点击
  const handleTabClick = (tab: string) => {
    const refs = {
      product: productRef,
      review: reviewRef,
      detail: detailRef,
      recommend: recommendRef,
    }

    const targetRef = refs[tab as keyof typeof refs]
    if (!targetRef?.current) return

    window.scrollTo({
      top: targetRef.current.offsetTop - 45, // 减去导航栏高度
      behavior: 'smooth',
    })
    setActiveTab(tab)
  }

  return (
    <div className="sticky top-0 z-50 transform-gpu will-change-transform">
      <NavBar
        backIcon={left ? left : <Arrow size={24} color="#000000" rotate={180} />}
        onBack={handleBack}
        right={right && <div className="float-end">{right}</div>}
        style={{ ...customStyle, backgroundColor: '#FFFFFF', '--height': '5.6rem' }}>
        {isProduct && showTabs ? (
          <div className="flex items-center justify-around gap-8">
            <button
              className={clsx('px-2 py-1', activeTab === 'product' && 'text-primary')}
              onClick={() => handleTabClick('product')}>
              商品
            </button>
            {hasReview && (
              <button
                className={clsx('px-2 py-1', activeTab === 'review' && 'text-primary')}
                onClick={() => handleTabClick('review')}>
                评价
              </button>
            )}
            {hasDetail && (
              <button
                className={clsx('px-2 py-1', activeTab === 'detail' && 'text-primary')}
                onClick={() => handleTabClick('detail')}>
                详情
              </button>
            )}

            {hasRecommend && (
              <button
                data-a={activeTab}
                className={clsx('px-2 py-1', activeTab === 'recommend' && 'text-primary')}
                onClick={() => handleTabClick('recommend')}>
                推荐
              </button>
            )}
          </div>
        ) : (
          <span>{title}</span>
        )}
      </NavBar>
    </div>
  )
}

export default ProductNavBar
