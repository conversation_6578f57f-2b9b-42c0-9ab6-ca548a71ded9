'use client'

import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  OrderReturnCarrier,
  resolveCatchMessage,
  sleep,
  useAddAfterSaleTrackMutation,
  useLoadingContext,
  userOrderReturnCarriersSelector,
  useToastContext,
} from '@ninebot/core'
import { useAppSelector } from '@ninebot/core/src/store/hooks'
import { Form, Input } from 'antd'

import { IconArrow, Modal } from '@/components'

import ChooseTrackCompany from './ChooseTrackCompany'

const TipIcon = () => {
  return (
    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6 1.5C3.23857 1.5 0.999999 3.73858 1 6.5C1 9.26143 3.23857 11.5 6 11.5C8.76143 11.5 11 9.26143 11 6.5C11 3.73857 8.76142 1.5 6 1.5Z"
        stroke="#DA291C"
        strokeLinejoin="round"
      />
      <path
        d="M6.5 9.05078L6.5 8.55078L6 8.05078L5.5 8.55078L5.5 9.05078L6.5 9.05078Z"
        fill="#DA291C"
      />
      <path
        d="M5.5 3.98118L5.5 7.07031L6 7.57031L6.5 7.07031L6.5 3.98118L5.5 3.98118Z"
        fill="#DA291C"
      />
    </svg>
  )
}

type TrackingModalProps = {
  visible: boolean
  returnId?: string
  onClose: () => void
  onSuccess?: () => void
}

interface TrackingFormData {
  trackCompany: string
  otherTrackName?: string
  trackNumber: string
}

/**
 * 填写物流信息Modal
 */
const TrackingModal = ({ visible, returnId, onClose, onSuccess }: TrackingModalProps) => {
  const trackCompany = useAppSelector(userOrderReturnCarriersSelector)
  const [form] = Form.useForm<TrackingFormData>()

  const getI18nString = useTranslations('Common')
  const loading = useLoadingContext()
  const toast = useToastContext()

  const [trackName, setTrackName] = useState<OrderReturnCarrier>()
  const [otherTrackName, setOtherTrackName] = useState('')
  const [trackNumber, setTrackNumber] = useState('')
  const [popupVisible, setPopupVisible] = useState(false)

  const [addAfterSaleTrack] = useAddAfterSaleTrackMutation()

  // Modal关闭时重置表单
  useEffect(() => {
    if (!visible) {
      form.resetFields()
      setTrackName(undefined)
      setOtherTrackName('')
      setTrackNumber('')
      setPopupVisible(false)
    }
  }, [visible, trackCompany, form])

  // Modal打开时设置初始值
  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        trackCompany: trackName?.carrier_title || '',
        otherTrackName: '',
        trackNumber: '',
      })
    }
  }, [visible, trackName, form])

  /**
   * 提交物流信息
   */
  const handleSubmit = () => {
    form.submit()
  }

  return (
    <Modal
      title={getI18nString('input_tracking_number')}
      isOpen={visible}
      onClose={onClose}
      width={600}
      modalProps={{
        destroyOnClose: true,
        styles: {
          content: {
            borderRadius: '12px',
            padding: 0,
          },
          header: {
            padding: '24px 24px 32px',
            borderBottom: 'none',
            marginBottom: 0,
          },
          body: {
            padding: '0 24px 24px',
          },
          footer: {
            padding: '32px 24px 24px',
            marginTop: 0,
          },
        },
      }}
      onConfirm={handleSubmit}
      okText={getI18nString('submit')}
      cancelText={getI18nString('cancel')}
      okButtonProps={{
        style: {
          height: '32px',
          padding: '8px 16px',
          width: '100px',
          fontSize: '14px',
          marginLeft: '12px',
        },
      }}
      cancelButtonProps={{
        style: {
          height: '32px',
          width: '100px',
          padding: '8px 16px',
          fontSize: '14px',
        },
      }}>
      <Form
        className="address-form space-y-base-32"
        form={form}
        layout="vertical"
        requiredMark={false}
        onFinish={(values) => {
          if (returnId) {
            loading.show()
            addAfterSaleTrack({
              id: returnId,
              company:
                trackName?.carrier_title === '其它'
                  ? otherTrackName
                  : String(trackName?.carrier_code),
              trackNo: values.trackNumber,
            })
              .unwrap()
              .then(async (res) => {
                loading.hide()
                await sleep(500)
                if (res?.requisitionTrack) {
                  toast.show({
                    icon: 'success',
                    content: getI18nString('submit_success'),
                  })
                  await sleep(500)
                  onClose()
                  onSuccess?.()
                } else {
                  toast.show({
                    icon: 'fail',
                    content: getI18nString('submit_failed'),
                  })
                }
              })
              .catch(async (error) => {
                loading.hide()
                await sleep(500)
                toast.show({
                  icon: 'fail',
                  content: resolveCatchMessage(error) as string,
                })
              })
          } else {
            toast.show({
              icon: 'fail',
              content: getI18nString('fetch_data_error'),
            })
          }
        }}>
        {/* 物流公司选择 */}
        <Form.Item
          name="trackCompany"
          label={
            <div className="flex items-center gap-1 text-base font-medium text-[#0F0F0F]">
              {getI18nString('track_company')}
              <span className="text-[#DA291C]">*</span>
            </div>
          }
          rules={[
            {
              required: true,
              validator: async () => {
                if (!trackName) {
                  return Promise.reject({
                    message: (
                      <div className="flex items-center gap-[4px]">
                        <TipIcon />
                        {getI18nString('please_choose_track_company')}
                      </div>
                    ),
                  })
                }
                return Promise.resolve()
              },
            },
          ]}>
          <ChooseTrackCompany
            trackCompany={trackCompany}
            popupVisible={popupVisible}
            onSelect={(value) => {
              setTrackName(value)
              form.setFieldsValue({ trackCompany: value?.carrier_title || '' })
            }}
            closePopup={() => {
              setPopupVisible(false)
            }}>
            <div className="relative w-full">
              <Input
                className="tracking-modal-input h-[48px] cursor-pointer rounded-[8px] border-[#D1D1D4]"
                disabled={false}
                value={trackName?.carrier_title || ''}
                placeholder={getI18nString('choose_track_company')}
                readOnly
                onClick={() => setPopupVisible(true)}
                suffix={<IconArrow size={24} rotate={popupVisible ? 180 : 0} />}
              />
            </div>
          </ChooseTrackCompany>
        </Form.Item>

        {/* 其它物流公司输入 */}
        {trackName?.carrier_title === '其它' && (
          <Form.Item
            name="otherTrackName"
            label={
              <div className="flex items-center gap-1 text-base font-medium text-[#0F0F0F]">
                {getI18nString('other_track')}
                <span className="text-[#DA291C]">*</span>
              </div>
            }
            rules={[
              {
                required: true,
                validator: async () => {
                  if (trackName?.carrier_title === '其它' && otherTrackName === '') {
                    return Promise.reject({
                      message: (
                        <div className="flex items-center gap-[4px]">
                          <TipIcon />
                          {getI18nString('please_input_track_company')}
                        </div>
                      ),
                    })
                  }
                  return Promise.resolve()
                },
              },
            ]}>
            <Input
              className="tracking-modal-input h-[48px] rounded-[8px] border-[#D1D1D4]"
              placeholder={getI18nString('input_track_company')}
              value={otherTrackName}
              onChange={(e) => {
                setOtherTrackName(e.target.value)
                form.setFieldsValue({ otherTrackName: e.target.value })
              }}
            />
          </Form.Item>
        )}

        {/* 物流单号输入 */}
        <Form.Item
          name="trackNumber"
          label={
            <div className="flex items-center gap-1 text-base font-medium text-[#0F0F0F]">
              {getI18nString('tracking_number')}
              <span className="text-[#DA291C]">*</span>
            </div>
          }
          rules={[
            {
              required: true,
              validator: async (_, value) => {
                if (!value || value.trim() === '') {
                  return Promise.reject({
                    message: (
                      <div className="flex items-center gap-[4px]">
                        <TipIcon />
                        {getI18nString('please_input_track_number')}
                      </div>
                    ),
                  })
                }
                return Promise.resolve()
              },
            },
          ]}>
          <Input
            className="tracking-modal-input h-[48px] rounded-[8px] border-[#D1D1D4]"
            value={trackNumber}
            onChange={(e) => {
              setTrackNumber(e.target.value)
              form.setFieldsValue({ trackNumber: e.target.value })
            }}
            placeholder={getI18nString('input_track_number')}
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default TrackingModal
