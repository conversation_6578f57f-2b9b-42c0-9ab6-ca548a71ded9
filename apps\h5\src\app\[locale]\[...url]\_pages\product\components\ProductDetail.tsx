import { RenderHtml } from '@ninebot/core'

import { CustomEmpty } from '@/components'

/**
 * 商品详情
 */
const ProductDetail = ({ content }: { content: string }) => {
  if (!content) {
    return <CustomEmpty description={'暂无商品详情'} />
  }

  // 为富文本中的图片/视频添加懒加载与轻量属性，降低滚动时内存与重绘压力
  const enhanced = content
    // img：懒加载 + 异步解码 + 安全策略 + 尺寸约束
    .replace(
      /<img\b/gi,
      '<img loading="lazy" decoding="async" referrerpolicy="no-referrer" style="max-width:100%;height:auto;display:block"',
    )
    // video：iOS 内联播放与延迟加载，避免自动预加载
    .replace(/<video\b/gi, '<video playsinline webkit-playsinline preload="none"')

  return (
    <div className="my-base flex flex-col gap-base-16 rounded-base-12 bg-white px-base-12 py-base-16 text-base">
      {/* <div className="font-miSansDemiBold450 text-lg">商品详情</div> */}
      <div className="rounded-lg">
        <RenderHtml content={enhanced} />
      </div>
    </div>
  )
}

export default ProductDetail
