'use client'
import React, { useMemo } from 'react'
import { Skeleton } from 'antd-mobile'

/**
 * Skeleton 组件
 *
 * 用于显示骨架屏。
 *
 * @param {Object} props
 * @param {string} [props.shape='default'] - 骨架屏的形状，支持 'circle'、'round'、'square' 和 'default'。
 * @param {boolean} [props.block=false] - 是否为块级元素，宽度自动填充父容器。
 * @param {number} [props.width] - 自定义宽度（可选）。
 * @param {number} [props.height] - 自定义高度（可选）。
 * @param {number} [props.borderRadius=8] - 圆角的半径（默认 8）。
 * @param {Object} [props.style] - 自定义样式。
 *
 * @returns {JSX.Element} Skeleton 组件
 */
const MobileSkeleton = ({
  shape = 'default',
  block = false,
  width = 100,
  height = 100,
  borderRadius = 8,
  style = {},
  animated = false,
}: {
  shape?: 'circle' | 'round' | 'square' | 'default'
  block?: boolean
  width?: number
  height?: number
  borderRadius?: number
  style?:
    | (React.CSSProperties & Partial<Record<'--width' | '--height' | '--border-radius', string>>)
    | undefined
  animated?: boolean
}) => {
  const skeletonStyle = useMemo(
    () => ({
      backgroundColor: '#F8F8F9',
      ...(width && { width }),
      ...(height && { height }),
      ...(shape === 'circle' && { borderRadius: 9999 }), // 圆形
      ...(shape === 'round' && { borderRadius }), // 圆角矩形
      ...(shape === 'square' && { borderRadius: 0 }), // 正方形
      ...(shape === 'default' && { borderRadius }), // 默认矩形
      ...(block && { width: '100%' }), // 如果是块级元素，宽度填满父容器
      ...style, // 自定义样式
    }),
    [width, height, shape, block, style, borderRadius],
  )

  return <Skeleton animated={animated} style={skeletonStyle} />
}

export default MobileSkeleton
