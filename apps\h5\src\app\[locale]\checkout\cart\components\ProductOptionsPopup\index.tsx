'use client'
import { useMemo } from 'react'
import { useTranslations } from 'next-intl'
import type { ProductDetailsData, StoreListItem } from '@ninebot/core'
import { isProductConfigurable } from '@ninebot/core/src/utils/productUtils'
import { Button, Image } from 'antd-mobile'

import {
  ColorSelector,
  CurrentPrice,
  InstallationService,
  SizeSelector,
  StorePickup,
  StoreSelectorPopup,
} from '@/businessComponents'
import { CustomPopup, CustomTag, Skeleton } from '@/components'
import type {
  ProductConfigOption,
  ProductConfigurableOption,
  ProductDetails,
  ProductOptionItem,
  ProductServiceParams,
  ProductStatus,
  SafeguardItem,
  Variant,
} from '@/types/product'

import { useCart } from '../../context/cartContext'
import { useProduct } from '../../hooks/useProduct'

const ProductOptionsPopup = () => {
  const {
    cartState: productStatus,
    setCartState: productApi,
    handleOptionsSwitch,
    handleRefresh,
  } = useCart()
  const {
    show,
    selectStore,
    productDetail,
    handlePopupClose,
    safeguardItems,
    productConfigurableOptions,
    productServiceOptions,
    handleSelectionChange,
    getStatusStock,
    isEnablebtn,
    handleAddCart,
    handleUpdateService,
    handleViewStorePop,
    deliveryMethodPickup,
    doorVisible,
    setDoorVisible,
    setSelectStore,
    updateCartOptionsRateLimit,
  } = useProduct(productStatus, productApi, handleOptionsSwitch, handleRefresh) as unknown as {
    show: boolean
    selectStore: StoreListItem | null
    productStatus: ProductStatus
    productDetail: ProductDetails & { variants?: Variant[] }
    handlePopupClose: () => void
    safeguardItems: SafeguardItem[]
    productConfigurableOptions: ProductConfigurableOption[]
    productServiceOptions: ProductConfigOption[]
    handleSelectionChange: (item: ProductOptionItem, optionId: string | number) => void
    getStatusStock: () => boolean
    isEnablebtn: () => boolean
    handleAddCart: () => void
    handleUpdateService: (params: ProductServiceParams) => void
    handleViewStorePop: () => void
    deliveryMethodPickup: boolean
    doorVisible: boolean
    setDoorVisible: () => void
    setSelectStore: () => void
    updateCartOptionsRateLimit: {
      isDisabled: boolean
      buttonText: string
    }
  }

  const getI18nString = useTranslations('Common')

  const price = productStatus.price
  const paymentNcoin =
    productStatus?.productItem?.paymeng_method?.includes('payment_method_ncoin') || false
  const servicePrice = productStatus.servicePrice || 0
  const storePrice = selectStore ? (selectStore as StoreListItem).price : 0
  // const salableQty = productStatus.salable_qty || 9999
  const isPromote = price ? price.final_price.value !== price.regular_price.value : false

  const onConfirmCallback = () => {
    productApi({
      ...productStatus,
      popVisible: true,
    })
  }

  const ProductInfo = useMemo(() => {
    return (
      <div className="flex items-center gap-base rounded-base-12 bg-white p-base-12">
        {productStatus.image && (
          <Image
            src={productStatus.image.url}
            alt="商品图片"
            width={60}
            height={60}
            className="rounded-lg object-cover"
          />
        )}
        <div className="flex flex-col gap-base">
          {price && (
            <CurrentPrice
              price={price}
              paymentNcoin={paymentNcoin}
              servicePrice={servicePrice}
              storePrice={storePrice}
              isPromote={isPromote}
            />
          )}
          {safeguardItems.length > 0 && (
            <div className="flex items-center gap-2">
              {safeguardItems.map(
                (clause) => clause.value && <CustomTag key={clause.value} text={clause.label} />,
              )}
            </div>
          )}
        </div>
      </div>
    )
  }, [productStatus, price, paymentNcoin, servicePrice, storePrice, isPromote, safeguardItems])

  const SelectedInfo = () => {
    return (
      <div>
        {productStatus.selectOptionName
          ? `${getI18nString('product_selected')}：${productStatus.selectOptionName}`
          : ''}
      </div>
    )
  }

  const FooterButton = () => (
    <Button
      color="primary"
      className={`nb-button w-full ${updateCartOptionsRateLimit.isDisabled ? 'nb-button-no-padding' : ''}`}
      onClick={getStatusStock() ? handleAddCart : handlePopupClose}
      disabled={getStatusStock() ? isEnablebtn() || updateCartOptionsRateLimit.isDisabled : true}>
      {getStatusStock() ? updateCartOptionsRateLimit.buttonText : getI18nString('product_sale_out')}
    </Button>
  )

  return (
    <>
      <CustomPopup
        showHeader
        visible={productStatus.popVisible}
        onClose={handlePopupClose}
        bodyStyle={{
          backgroundColor: '#F3F3F4',
        }}
        headerClassName="border-none"
        headLeft={<SelectedInfo />}
        footer={<FooterButton />}>
        {show && productDetail ? (
          <div className="flex flex-col gap-base px-base-12 pb-base-12">
            {ProductInfo}

            {/* 商品选项 */}
            <div className="flex flex-col gap-12 rounded-base-12 bg-white px-base-12 py-base-16">
              {isProductConfigurable(productDetail as unknown as ProductDetailsData) &&
                productConfigurableOptions?.map((option) => (
                  <div key={option.attribute_id}>
                    <div className="mb-base-12 text-base">{option.label}</div>
                    {option.attribute_code.includes('color') ? (
                      <ColorSelector
                        optionItems={option.values}
                        id={option.attribute_id}
                        variants={productDetail.variants || []}
                        productStatus={productStatus}
                        onSelectionChange={handleSelectionChange}
                      />
                    ) : (
                      <SizeSelector
                        optionItems={option.values}
                        id={option.attribute_id}
                        productStatus={productStatus}
                        onSelectionChange={handleSelectionChange}
                      />
                    )}
                  </div>
                ))}

              {/* 安装服务 */}
              {productServiceOptions.length > 0 && (
                <InstallationService
                  productConfigOptions={productServiceOptions}
                  productStatus={productStatus}
                  handleUpdateService={handleUpdateService}
                />
              )}

              {/* 门店选择 */}
              {deliveryMethodPickup && (
                <div className="flex flex-col gap-base-12">
                  <div className="font-miSansDemiBold450 text-base">选择门店</div>

                  <StorePickup
                    selectedStore={selectStore}
                    handleViewStorePop={handleViewStorePop}
                  />
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="flex flex-col gap-base px-base-12 pb-base-24">
            {Array.from({ length: 2 }, (_, index) => ({
              id: index,
            })).map((item) => (
              <Skeleton
                key={item.id}
                style={{
                  borderRadius: 12,
                  height: 60,
                  width: '100%',
                  marginTop: 12,
                }}
              />
            ))}
          </div>
        )}
      </CustomPopup>

      {deliveryMethodPickup && (
        <StoreSelectorPopup
          doorVisible={doorVisible}
          setDoorVisible={setDoorVisible}
          selectStore={selectStore}
          setSelectStore={setSelectStore}
          productId={productStatus.id}
          setVisibleAddCartPop={onConfirmCallback}
        />
      )}
    </>
  )
}

export default ProductOptionsPopup
